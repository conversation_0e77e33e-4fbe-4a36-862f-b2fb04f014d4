using UnityEngine;
using UnityEngine.UI;

public class WeatherSystem : MonoBehaviour
{
    public ParticleSystem rainParticleSystem;
    public ParticleSystem airstormParticleSystem;
    public AudioSource rainAudio, airstormAudio;
    public Material sunnySkybox;
    public Material rainSkybox;
    public Material airstormSkybox;
    public Light DirectionalLight;
    public static WeatherSystem instance;

    public Button sunnyButton;
    public Button rainButton;
    public Button airstormButton;

    private enum WeatherType { Sunny, Rain, Airstorm }

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
    }

    private void Start()
    {
        StopAllParticles();
        sunnyButton.GetComponent<Button>().enabled = false;
        // Assign button listeners
        if (sunnyButton != null)
            sunnyButton.onClick.AddListener(() => ActivateSunny());
        else
            Debug.LogWarning("Sunny Button is not assigned in the Inspector!");

        if (rainButton != null)
            rainButton.onClick.AddListener(() => ActivateRain());
        else
            Debug.LogWarning("Rain Button is not assigned in the Inspector!");

        if (airstormButton != null)
            airstormButton.onClick.AddListener(() => ActivateAirstorm());
        else
            Debug.LogWarning("Airstorm Button is not assigned in the Inspector!");
    }

    public void ActivateRain()
    {
        Invoke("Rain", 0.1f);
    }

    private void Rain()
    {
        StopAllParticles();
        RenderSettings.skybox = rainSkybox;

        if (DirectionalLight != null)
            DirectionalLight.intensity = 0.15f;

        RenderSettings.ambientEquatorColor = new Color32(135, 128, 128, 255);
        RenderSettings.fog = true;

        rainParticleSystem.gameObject.SetActive(true);
        rainAudio.Play();
    }

    public void ActivateAirstorm()
    {
        Invoke("Storm", 0.1f);
    }

    private void Storm()
    {
        StopAllParticles();
        RenderSettings.skybox = airstormSkybox;

        airstormParticleSystem.gameObject.SetActive(true);
        airstormAudio.Play();

        RenderSettings.fog = false;
    }

    public void ActivateSunny()
    {
        Invoke("Sunny", 0.1f);
    }

    private void Sunny()
    {
        StopAllParticles();

        RenderSettings.skybox = sunnySkybox;
        RenderSettings.fog = false;
        DirectionalLight.intensity = 1f;
    }

    private void StopAllParticles()
    {
        if (rainParticleSystem.gameObject.activeInHierarchy)
        {
            rainParticleSystem.gameObject.SetActive(false);
            rainAudio.Stop();
        }

        if (airstormParticleSystem.gameObject.activeInHierarchy)
        {
            airstormParticleSystem.gameObject.SetActive(false);
            airstormAudio.Stop();
        }
    }
    public void Weathersunny()
    {
        sunnyButton.GetComponent<Image>().enabled = false;
        airstormButton.GetComponent<Image>().enabled = true;
        rainButton.GetComponent<Image>().enabled = true;
        sunnyButton.transform.GetChild(1).gameObject.SetActive(true);
        airstormButton.transform.GetChild(1).gameObject.SetActive(false);
        rainButton.transform.GetChild(1).gameObject.SetActive(false);
        sunnyButton.GetComponent<Button>().enabled = false;
        airstormButton.GetComponent<Button>().enabled = true;
        rainButton.GetComponent<Button>().enabled = true;

    }
    public void WeatherAir()
    {
        sunnyButton.GetComponent<Image>().enabled = true;
        airstormButton.GetComponent<Image>().enabled = false;
        rainButton.GetComponent<Image>().enabled = true;
        sunnyButton.transform.GetChild(1).gameObject.SetActive(false);
        airstormButton.transform.GetChild(1).gameObject.SetActive(true);
        rainButton.transform.GetChild(1).gameObject.SetActive(false);
        sunnyButton.GetComponent<Button>().enabled = true;
        airstormButton.GetComponent<Button>().enabled = false;
        rainButton.GetComponent<Button>().enabled = true;

    }
    public void WeatherRain()
    {
        sunnyButton.GetComponent<Image>().enabled = true;
        airstormButton.GetComponent<Image>().enabled = true;
        rainButton.GetComponent<Image>().enabled = false;
        sunnyButton.transform.GetChild(1).gameObject.SetActive(false);
        airstormButton.transform.GetChild(1).gameObject.SetActive(false);
        rainButton.transform.GetChild(1).gameObject.SetActive(true);
        sunnyButton.GetComponent<Button>().enabled = true;
        airstormButton.GetComponent<Button>().enabled = true;
        rainButton.GetComponent<Button>().enabled = false;
    }
    public void Ad()
    {
        AdsController.Instance.ShowInterstitialAd_Admob();
    }
}
