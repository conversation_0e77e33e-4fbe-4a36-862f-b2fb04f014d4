Shader "Custom/RealisticBrakelight"
{
    Properties
    {
        _MainTex ("Brake Light Texture", 2D) = "white" {}
        _BrakeLightColor ("Brake Light Color", Color) = (1, 0.1, 0.1, 1)
        _EmissionIntensity ("Emission Intensity", Range(0, 10)) = 2.0
        _FresnelPower ("Fresnel Power", Range(0.1, 5)) = 2.0
        _FresnelIntensity ("Fresnel Intensity", Range(0, 2)) = 1.0
        _Glossiness ("Smoothness", Range(0,1)) = 0.8
        _Metallic ("Metallic", Range(0,1)) = 0.1
        _BrakeIntensity ("Brake Intensity", Range(0, 1)) = 0.0
        _PulseSpeed ("Pulse Speed", Range(0, 10)) = 0.0
        _InnerGlow ("Inner Glow", Range(0, 2)) = 0.5
    }
    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" }
        LOD 200
        Blend SrcAlpha OneMinusSrcAlpha

        CGPROGRAM
        // Physically based Standard lighting model with emission
        #pragma surface surf Standard fullforwardshadows alpha:fade

        // Use shader model 3.0 target for better lighting
        #pragma target 3.0

        sampler2D _MainTex;
        fixed4 _BrakeLightColor;
        half _EmissionIntensity;
        half _FresnelPower;
        half _FresnelIntensity;
        half _Glossiness;
        half _Metallic;
        half _BrakeIntensity;
        half _PulseSpeed;
        half _InnerGlow;

        struct Input
        {
            float2 uv_MainTex;
            float3 viewDir;
            float3 worldNormal;
        };

        UNITY_INSTANCING_BUFFER_START(Props)
        UNITY_INSTANCING_BUFFER_END(Props)

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // Sample the texture
            fixed4 tex = tex2D(_MainTex, IN.uv_MainTex);

            // Calculate fresnel effect for realistic light falloff
            float fresnel = 1.0 - saturate(dot(normalize(IN.viewDir), IN.worldNormal));
            fresnel = pow(fresnel, _FresnelPower) * _FresnelIntensity;

            // Create pulsing effect if enabled
            float pulse = 1.0;
            if (_PulseSpeed > 0)
            {
                pulse = 0.5 + 0.5 * sin(_Time.y * _PulseSpeed);
            }

            // Calculate brake light intensity
            float brakeEffect = _BrakeIntensity * pulse;

            // Base color with texture
            fixed3 baseColor = tex.rgb * _BrakeLightColor.rgb;

            // Add inner glow effect
            float centerGlow = 1.0 - length(IN.uv_MainTex - 0.5) * 2.0;
            centerGlow = saturate(centerGlow * _InnerGlow);

            // Combine all effects
            fixed3 finalColor = baseColor * (1.0 + centerGlow);

            // Calculate emission
            fixed3 emission = finalColor * _EmissionIntensity * brakeEffect;
            emission += fresnel * _BrakeLightColor.rgb * brakeEffect;

            // Set surface properties
            o.Albedo = finalColor * (1.0 - brakeEffect * 0.5);
            o.Emission = emission;
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            o.Alpha = tex.a * _BrakeLightColor.a * (0.3 + brakeEffect * 0.7);
        }
        ENDCG
    }
    FallBack "Transparent/Diffuse"
}
