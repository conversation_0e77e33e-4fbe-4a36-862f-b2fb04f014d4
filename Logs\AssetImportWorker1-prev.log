Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker1.log
-srvPort
64508
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 128.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56832
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012206 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 417 ms
Refreshing native plugins compatible for Editor in 116.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.534 seconds
Domain Reload Profiling:
	ReloadAssembly (1534ms)
		BeginReloadAssembly (160ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1197ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (191ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (889ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (532ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (116ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (166ms)
				ProcessInitializeOnLoadMethodAttributes (72ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015410 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 113.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.737 seconds
Domain Reload Profiling:
	ReloadAssembly (2739ms)
		BeginReloadAssembly (235ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (2310ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (357ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (109ms)
			SetupLoadedEditorAssemblies (1644ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (114ms)
				BeforeProcessingInitializeOnLoad (116ms)
				ProcessInitializeOnLoadAttributes (1277ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 6.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3708 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4129.
Memory consumption went from 175.0 MB to 174.3 MB.
Total: 7.387200 ms (FindLiveObjects: 0.440800 ms CreateObjectMapping: 0.270700 ms MarkObjects: 5.836900 ms  DeleteObjects: 0.837500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 100535.899648 seconds.
  path: Assets/Scenes/Farming mod.unity
  artifactKey: Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/Farming mod.unity using Guid(ebb0204f92bb3ab47bfb1a7825f65c1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2cc825a7ac39884494a10e685080ef89') in 0.023181 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015725 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.404 seconds
Domain Reload Profiling:
	ReloadAssembly (2405ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2001ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (362ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (84ms)
			SetupLoadedEditorAssemblies (1362ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1139ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3666 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4144.
Memory consumption went from 171.4 MB to 170.7 MB.
Total: 8.481000 ms (FindLiveObjects: 0.549800 ms CreateObjectMapping: 0.278600 ms MarkObjects: 6.830400 ms  DeleteObjects: 0.821000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 226.341027 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_Highway_Road_SHighway_Road_N2023054508611406.terrainlayer
  artifactKey: Guid(ede9ebed613327c43bb1672e9ff6f8c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/layer_Highway_Road_SHighway_Road_N2023054508611406.terrainlayer using Guid(ede9ebed613327c43bb1672e9ff6f8c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ace005e0b4e3a4e2570518b5bddeea78') in 0.194107 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Newasset/NewLayer.terrainlayer
  artifactKey: Guid(2f16cc1755ffd7d46a9166757e599dd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Newasset/NewLayer.terrainlayer using Guid(2f16cc1755ffd7d46a9166757e599dd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f0170912847e019783c26d3c3621ee01') in 0.039446 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Newasset/NewLayer 1.terrainlayer
  artifactKey: Guid(1ff488b3be452eb4996cac831083a3ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Newasset/NewLayer 1.terrainlayer using Guid(1ff488b3be452eb4996cac831083a3ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f7a508db06f64c8717bcec7b2f7cd932') in 0.120802 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/newterrianlayer/NewLayer 1.terrainlayer
  artifactKey: Guid(7088d766b2e54154ead528189a29cdb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/NewLayer 1.terrainlayer using Guid(7088d766b2e54154ead528189a29cdb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3896754e7798aef1152ad5ad6f725a69') in 0.067608 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer 5.terrainlayer
  artifactKey: Guid(6729cfb4e05dae248903ee02b7fda1c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer 5.terrainlayer using Guid(6729cfb4e05dae248903ee02b7fda1c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '529d0b5cc120503f7750e284a3d1ecd5') in 0.038677 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer.terrainlayer
  artifactKey: Guid(c7b2de928c9cda64bb4ec98b8268a0cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer.terrainlayer using Guid(c7b2de928c9cda64bb4ec98b8268a0cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7708fdda745115b63b174f21a162245d') in 0.068974 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/newterrianlayer/NewLayer 8.terrainlayer
  artifactKey: Guid(7549f2dda724f2b498435c8193584788) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/NewLayer 8.terrainlayer using Guid(7549f2dda724f2b498435c8193584788) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2643b6ff3e642ba142d097c9043e107d') in 0.077380 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer 4.terrainlayer
  artifactKey: Guid(33ce44474c39b0a45aad1483250e5501) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer 4.terrainlayer using Guid(33ce44474c39b0a45aad1483250e5501) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1a2fc26d57c5c1f88e6bae830f93a945') in 0.041964 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 7.690524 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Decals/5.png
  artifactKey: Guid(b1606efe4dd694246a44ffb8aea83f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Decals/5.png using Guid(b1606efe4dd694246a44ffb8aea83f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b426e0d7cd84d091d204a9e7830fc58b') in 0.720763 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.018141 seconds.
  path: Assets/Confetti FX/Textures/confettiblurred4x4.png
  artifactKey: Guid(7e3039b2a7bbde4448fb3ed6175ca0d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Textures/confettiblurred4x4.png using Guid(7e3039b2a7bbde4448fb3ed6175ca0d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eca515a23187ea62d5605b20b4e6c8e2') in 0.441876 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/BuildReport/GUI/NativeSkin/Dark/Textfield-Focused.png
  artifactKey: Guid(80003ef802d6179429eed4d66bf577d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/NativeSkin/Dark/Textfield-Focused.png using Guid(80003ef802d6179429eed4d66bf577d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc74072368437ea459e7a6b3fc37974f') in 0.026398 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/3RDMODEASSET/TRACTOR TOCHAN/DefaultMaterial_albedo.jpeg
  artifactKey: Guid(089abb2c881d87847935712b815c3aed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/3RDMODEASSET/TRACTOR TOCHAN/DefaultMaterial_albedo.jpeg using Guid(089abb2c881d87847935712b815c3aed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'aa4c9bb26a35fc9c1664d3c2e6d9461a') in 0.042532 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Falloff 1.psd
  artifactKey: Guid(0615a28918048f84cb37e59067f2f07d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Falloff 1.psd using Guid(0615a28918048f84cb37e59067f2f07d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a59d574a7ff639516f0649a5fe7dd10') in 0.041617 seconds 
Number of asset objects unloaded after import = 2
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0