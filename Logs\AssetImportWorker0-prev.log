Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker0.log
-srvPort
64508
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 133.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56764
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012621 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 419 ms
Refreshing native plugins compatible for Editor in 116.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.533 seconds
Domain Reload Profiling:
	ReloadAssembly (1534ms)
		BeginReloadAssembly (162ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1203ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (190ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (896ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (540ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (116ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (165ms)
				ProcessInitializeOnLoadMethodAttributes (73ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.023200 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 112.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.682 seconds
Domain Reload Profiling:
	ReloadAssembly (2683ms)
		BeginReloadAssembly (227ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (45ms)
		EndReloadAssembly (2277ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (407ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (122ms)
			SetupLoadedEditorAssemblies (1540ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (113ms)
				BeforeProcessingInitializeOnLoad (108ms)
				ProcessInitializeOnLoadAttributes (1194ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3708 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4129.
Memory consumption went from 175.0 MB to 174.3 MB.
Total: 7.852300 ms (FindLiveObjects: 0.448300 ms CreateObjectMapping: 0.269900 ms MarkObjects: 6.303500 ms  DeleteObjects: 0.829800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 100542.382036 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa00c06fecae7ce4cbc852627ce7ff07') in 0.035630 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 8.616009 seconds.
  path: Assets/Scenes/Game scene
  artifactKey: Guid(35a9df29f6ae87343b00f26ecf3ae89f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/Game scene using Guid(35a9df29f6ae87343b00f26ecf3ae89f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f9d378396724362eb7cabd49376a46af') in 0.013544 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 48.455312 seconds.
  path: Assets/kachra
  artifactKey: Guid(ba4a4e986cd336a459ba453454002457) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/kachra using Guid(ba4a4e986cd336a459ba453454002457) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '66d4af6d1a640bfba8fd13dffe4f8196') in 0.012918 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014167 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.508 seconds
Domain Reload Profiling:
	ReloadAssembly (2509ms)
		BeginReloadAssembly (262ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (79ms)
		EndReloadAssembly (2077ms)
			LoadAssemblies (156ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (392ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1399ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1173ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3666 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4144.
Memory consumption went from 171.4 MB to 170.7 MB.
Total: 8.359800 ms (FindLiveObjects: 0.517800 ms CreateObjectMapping: 0.270800 ms MarkObjects: 6.824900 ms  DeleteObjects: 0.745300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 29.947504 seconds.
  path: Assets/Game scene/New Folder
  artifactKey: Guid(2190b19cdc5e93c408f790a3a2b018d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New Folder using Guid(2190b19cdc5e93c408f790a3a2b018d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'efbca8a24f61bc4e751ea3bf6ea8f448') in 0.035951 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 14.035696 seconds.
  path: Assets/Game scene/New enviroment texture
  artifactKey: Guid(2190b19cdc5e93c408f790a3a2b018d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture using Guid(2190b19cdc5e93c408f790a3a2b018d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0eeb4f7c9892027311503733b0add2e2') in 0.020266 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 103.525188 seconds.
  path: Assets/Game scene/New enviroment texture/fileld 1.png
  artifactKey: Guid(36959ea8f08e6c7498c8f91b9187c94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture/fileld 1.png using Guid(36959ea8f08e6c7498c8f91b9187c94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7b0511a15de04aeb4ac7314d57034e8a') in 0.128113 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 14.778577 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_Grass_DGrass_Ncd2d2d038030d602.terrainlayer
  artifactKey: Guid(eb0a19a1d84ef524ba8b48bddc9e03e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/layer_Grass_DGrass_Ncd2d2d038030d602.terrainlayer using Guid(eb0a19a1d84ef524ba8b48bddc9e03e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7eac93fba4312f79bbed473cd201929') in 0.095257 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/newterrianlayer/NewLayer.terrainlayer
  artifactKey: Guid(11575f290056a3040b92e68b3985be92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/NewLayer.terrainlayer using Guid(11575f290056a3040b92e68b3985be92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b05d66f11bf3b4e8978926cb0b8bd881') in 0.083493 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/newterrianlayer/NewLayer 11.terrainlayer
  artifactKey: Guid(cf0883fcbb34f6e45bc67339e6460e16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/NewLayer 11.terrainlayer using Guid(cf0883fcbb34f6e45bc67339e6460e16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '258ddd02db16f7d986e835568a1d0587') in 0.021062 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/newterrianlayer/NewLayer 9.terrainlayer
  artifactKey: Guid(a29f7644578020040817461de38483be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/NewLayer 9.terrainlayer using Guid(a29f7644578020040817461de38483be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc1980c83604b1cadb8d9a6cac59edc0') in 0.036013 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer 1.terrainlayer
  artifactKey: Guid(f333f373fb32aa74aab518047044fb1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer 1.terrainlayer using Guid(f333f373fb32aa74aab518047044fb1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b12ca72ac8efe4e7207dc29fa77be3eb') in 0.093558 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer 2.terrainlayer
  artifactKey: Guid(cb270d4dedfd0c34da3e53fc051ac7e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer 2.terrainlayer using Guid(cb270d4dedfd0c34da3e53fc051ac7e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03193d331384255976618a2beaa4944d') in 0.054482 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_TerrainAutoUpgrade/NewLayer 3.terrainlayer
  artifactKey: Guid(89ebe4d99ae86db44ab5657c144d1b8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/NewLayer 3.terrainlayer using Guid(89ebe4d99ae86db44ab5657c144d1b8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '53c399f62ca7d79470b9d534309404c0') in 0.060660 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_TerrainAutoUpgrade/layer_SandAlbedoMudRockyNormals2023054508611406.terrainlayer
  artifactKey: Guid(f2a7fe28ae8251040b1ae0d2fdc7bf6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/_TerrainAutoUpgrade/layer_SandAlbedoMudRockyNormals2023054508611406.terrainlayer using Guid(f2a7fe28ae8251040b1ae0d2fdc7bf6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c785f362f37a4a5a0673e690d972d375') in 0.033283 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 7.718718 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Decals/4.png
  artifactKey: Guid(b8ab982571f1f104f8a4022c6d421403) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Decals/4.png using Guid(b8ab982571f1f104f8a4022c6d421403) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c96a2aaf174f29e3191f931ed96678a1') in 0.728002 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.005985 seconds.
  path: Assets/Confetti FX/Textures/confetti4x4.png
  artifactKey: Guid(75b5de378686eb749bfca4c52985999b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Textures/confetti4x4.png using Guid(75b5de378686eb749bfca4c52985999b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b6b5c0cc214f0b3c8be6f73e2625f360') in 0.511258 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/Modification.png
  artifactKey: Guid(fc9d945ae17d52b479bca15ae64c1f4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/Modification.png using Guid(fc9d945ae17d52b479bca15ae64c1f4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bd4b3b595fa050e3631970d943170b85') in 0.025116 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/snow_field_puresky_1k.exr
  artifactKey: Guid(9111079137d185d41b5be56bae0f7237) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/snow_field_puresky_1k.exr using Guid(9111079137d185d41b5be56bae0f7237) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a0d2ad3528737fb6e0ea5c65461f7a8') in 0.066226 seconds 
Number of asset objects unloaded after import = 2
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0