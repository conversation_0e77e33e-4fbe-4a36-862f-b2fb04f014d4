Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker4.log
-srvPort
64508
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 138.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56816
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.014016 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 480 ms
Refreshing native plugins compatible for Editor in 110.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.648 seconds
Domain Reload Profiling:
	ReloadAssembly (1649ms)
		BeginReloadAssembly (168ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1289ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (220ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (955ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (600ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (111ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (169ms)
				ProcessInitializeOnLoadMethodAttributes (73ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015642 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 107.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.713 seconds
Domain Reload Profiling:
	ReloadAssembly (2714ms)
		BeginReloadAssembly (220ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (2302ms)
			LoadAssemblies (184ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (384ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (104ms)
			SetupLoadedEditorAssemblies (1598ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (108ms)
				BeforeProcessingInitializeOnLoad (112ms)
				ProcessInitializeOnLoadAttributes (1251ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 3.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3708 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4129.
Memory consumption went from 174.7 MB to 174.0 MB.
Total: 7.655000 ms (FindLiveObjects: 0.503400 ms CreateObjectMapping: 0.292200 ms MarkObjects: 6.072000 ms  DeleteObjects: 0.786300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 100770.890102 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Decals/1.png
  artifactKey: Guid(5382c3ccd633724459e09255f099496d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Decals/1.png using Guid(5382c3ccd633724459e09255f099496d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '01e783d956106cf1395075868b977621') in 0.787425 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.942965 seconds.
  path: Assets/RealisticCarControllerV3/Textures/Mobile Textures/ArrowLeft.png
  artifactKey: Guid(0ca44adc2f47b6443b42b6ee81cd12ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Textures/Mobile Textures/ArrowLeft.png using Guid(0ca44adc2f47b6443b42b6ee81cd12ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9995c4117f9f52464a16971a8e430ab2') in 0.024565 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/assets/PopulationSystem/CharacterPack01/Textures/Farmer_diff.tga
  artifactKey: Guid(02b702ede5d7429419c8ed244b0198a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/assets/PopulationSystem/CharacterPack01/Textures/Farmer_diff.tga using Guid(02b702ede5d7429419c8ed244b0198a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b0ad6768ccbcab91b4592a0d774c350') in 0.047873 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/GameMy/Tx_Bonfire.jpg
  artifactKey: Guid(a6cec7714bb3d924d802a17407292c5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Tx_Bonfire.jpg using Guid(a6cec7714bb3d924d802a17407292c5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e9257aedbfb01c7544d882f16a69f57') in 0.322808 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/tractor ui/images (2)/images/FAILED.jpg
  artifactKey: Guid(eda2af7721bac334e8092b27823f0132) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/tractor ui/images (2)/images/FAILED.jpg using Guid(eda2af7721bac334e8092b27823f0132) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9264dd140e38a9985a3be4c3f4208b77') in 0.295250 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 764.529217 seconds.
  path: Assets/Game scene/New enviroment texture/Plough.fbx
  artifactKey: Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture/Plough.fbx using Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ef4a9aac15cc56ee0a93aced8ea9ddcb') in 0.146388 seconds 
Number of asset objects unloaded after import = 18
========================================================================
Received Import Request.
  Time since last request: 5.606779 seconds.
  path: Assets/Game scene/New enviroment texture/Plough.fbx
  artifactKey: Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture/Plough.fbx using Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '535e2ce1219098493c41e33aff69654d') in 0.057059 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 21.249219 seconds.
  path: Assets/Game scene/New enviroment texture/Plough.fbx
  artifactKey: Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture/Plough.fbx using Guid(a46228d8b0d392f439c43f79cce2a76a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7107ce4c72a9d9c5807302f6b20ca38d') in 0.053662 seconds 
Number of asset objects unloaded after import = 20
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014028 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.611 seconds
Domain Reload Profiling:
	ReloadAssembly (2613ms)
		BeginReloadAssembly (406ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (15ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (136ms)
		EndReloadAssembly (2045ms)
			LoadAssemblies (186ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (411ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1358ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1135ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3666 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4193.
Memory consumption went from 176.0 MB to 175.3 MB.
Total: 9.399100 ms (FindLiveObjects: 0.654200 ms CreateObjectMapping: 0.265700 ms MarkObjects: 7.600400 ms  DeleteObjects: 0.877600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 11038.955390 seconds.
  path: Assets/TRACTOR1.fbx
  artifactKey: Guid(c9413532aa8f1c544ac8e5e48f759c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/TRACTOR1.fbx using Guid(c9413532aa8f1c544ac8e5e48f759c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6ea14de1a5dc430cb938890c9339b1c2') in 0.510698 seconds 
Number of asset objects unloaded after import = 145
========================================================================
Received Import Request.
  Time since last request: 1398.796179 seconds.
  path: Assets/#_AdManager_#/Prefabs/Canvas_Plugin.prefab
  artifactKey: Guid(abe6550e9314deb4aa2c8ca9c2670b70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/#_AdManager_#/Prefabs/Canvas_Plugin.prefab using Guid(abe6550e9314deb4aa2c8ca9c2670b70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b7b8657154e02e0aed03df71ffe91ab9') in 0.704549 seconds 
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 0.516936 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionGreen.prefab
  artifactKey: Guid(7411fc9f1ee9c3f44ab0420fd6af47c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionGreen.prefab using Guid(7411fc9f1ee9c3f44ab0420fd6af47c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f79685639e51db7840ec9674603bb745') in 0.411660 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionHalloween.prefab
  artifactKey: Guid(0d57e66d7df915b4aabb02005dd9f8b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionHalloween.prefab using Guid(0d57e66d7df915b4aabb02005dd9f8b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6cd24b8d771783a99728d0a2906414ce') in 2.086557 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionMagic.prefab
  artifactKey: Guid(da4957823d91d8a4c878dec336e7dfe1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionMagic.prefab using Guid(da4957823d91d8a4c878dec336e7dfe1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '73aaac152ee61bf062d4777c8845a193') in 0.104839 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionPurple.prefab
  artifactKey: Guid(0d069c3f7af0a6d4ea124420c9c45b78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionPurple.prefab using Guid(0d069c3f7af0a6d4ea124420c9c45b78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd5a3d3554bdbd5ac8d8e5c802e4c7c5e') in 0.245005 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionMix.prefab
  artifactKey: Guid(1805b6a59d8bf1e4fabb266117b0e2cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionMix.prefab using Guid(1805b6a59d8bf1e4fabb266117b0e2cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc48b51548186ab94f4f5ee05d769269') in 0.091693 seconds 
Number of asset objects unloaded after import = 18
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionRainbow.prefab
  artifactKey: Guid(cfd858b69e847b4468421c2fd8411837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/2D/Explosion/ConfettiExplosionRainbow.prefab using Guid(cfd858b69e847b4468421c2fd8411837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '06a388e2d9b88be0acadd503f6560895') in 0.052995 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionWhite.prefab
  artifactKey: Guid(fda59ba77c73b064b9aa55c3248adfbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Prefabs/3D/Explosion/ConfettiExplosionWhite.prefab using Guid(fda59ba77c73b064b9aa55c3248adfbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0759dd68c9c396878cd0be4a08be5bf3') in 0.201445 seconds 
Number of asset objects unloaded after import = 8
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Confetti FX/Demo/Prefabs/DemoConfettiExplosionRomanticLooped.prefab
  artifactKey: Guid(8eefef3e76bdac048b07ca8e862d24db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Demo/Prefabs/DemoConfettiExplosionRomanticLooped.prefab using Guid(8eefef3e76bdac048b07ca8e862d24db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2f29cf18e445f2d1ebb9a31d06277602') in 0.830621 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Confetti FX/Demo/Prefabs/DemoExplosionRainbowLooped.prefab
  artifactKey: Guid(4de99bf90339ab043b4926edab6c68d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Confetti FX/Demo/Prefabs/DemoExplosionRainbowLooped.prefab using Guid(4de99bf90339ab043b4926edab6c68d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '70cabb65a905533dc57b1881514bacb6') in 0.178760 seconds 
Number of asset objects unloaded after import = 14
========================================================================
Received Import Request.
  Time since last request: 0.288506 seconds.
  path: Assets/Plough.fbx
  artifactKey: Guid(823e7459794b0274c9c0ac8e4b3bc73f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plough.fbx using Guid(823e7459794b0274c9c0ac8e4b3bc73f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3d9d5a316e2d61b672efaf3232487c98') in 0.066230 seconds 
Number of asset objects unloaded after import = 19
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0